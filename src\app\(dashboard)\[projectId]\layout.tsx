"use server";

import DocsBot from "@/components/docsBotAi";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { cookies } from "next/headers";
import { getUserProjects } from "@/lib/supabase/userProjects";
import type { Project } from "@/contexts/ProjectContext/types";
import { notFound } from "next/navigation";
import { LayoutWrapper } from "@/components/LayoutWrapper";
import { fetchProjectInfo } from "@/utils/create-repo/cloudflare";

export default async function DashboardLayout({
  children,
  params: paramsPromise,
}: {
  children: React.ReactNode;
  params: Promise<{ projectId: string }>;
}) {
  const params = await paramsPromise;
  const projectId = params.projectId;

  const cookieStore = await cookies();
  const defaultOpen = cookieStore.get("sidebar_state")?.value === "true";

  if (!projectId) {
    notFound();
  }

  const projects: Project[] | null = await getUserProjects();

  const projectExists = projects?.some((p) => p.id.toString() === projectId);

  if (!projectExists) {
    notFound();
  }

  // Busca informações do projeto no Cloudflare (Web Analytics + Pages)
  const currentProject = projects?.find((p) => p.id.toString() === projectId);
  let projectInfo = {
    siteName: undefined as string | undefined,
    siteTag: undefined as string | undefined,
    hasWebAnalytics: false as boolean | undefined,
  };

  if (currentProject) {
    try {
      projectInfo = await fetchProjectInfo(currentProject.project_name);
    } catch (error) {
      console.error(
        `Error fetching project info for ${currentProject.project_name}:`,
        error
      );
    }
  }

  return (
    <SidebarProvider defaultOpen={defaultOpen}>
      <AppSidebar />
      <main className="w-full h-full pt-6 md:pt-10 px-4 md:px-6 overflow-x-hidden">
        <SidebarTrigger />
        <DocsBot />
        <LayoutWrapper projectId={projectId} projectInfo={projectInfo}>
          {children}
        </LayoutWrapper>
      </main>
    </SidebarProvider>
  );
}
