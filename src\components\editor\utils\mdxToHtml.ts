import { unified } from "unified";
import remarkParse from "remark-parse";
import remarkMdx from "remark-mdx";
import remarkFrontmatter from "remark-frontmatter";
import remarkRehype from "remark-rehype";
import rehypeStringify from "rehype-stringify";
import { visit } from "unist-util-visit";
import { load } from "js-yaml";
import type { VFile } from "vfile";
import type { Root as MdastRoot } from "mdast";
import type {
  Root as HastRoot,
  Element as HastElement,
  Properties,
  Parent as HastParent,
  Text as HastText,
} from "hast";
import { toHtml } from "hast-util-to-html";

// Interface para atributos JSX do MDX
interface MdxJsxAttribute {
  name: string;
  value: string | number | boolean | { value: unknown } | null | undefined;
}

// Helper para converter camelCase para kebab-case (ex: iconType -> icon-type)
const toKebabCase = (str: string) =>
  str.replace(/[A-Z]/g, (letter) => `-${letter.toLowerCase()}`);

// Generate consistent IDs for components
const generateStableId = (type: string, index: number): string => {
  return `${type}-${index}-${Date.now()}`;
};

// Helper function to convert <br> tags to paragraph breaks in string content
const convertBrToPInString = (content: string): string => {
  if (!content) return content;
  return content
    .replace(/<br\s*\/?>/g, "</p><p>")
    .replace(/^<\/p>/, "")
    .replace(/<p>$/, "");
};

// Context for tracking component indices
interface TransformContext {
  accordionIndex: number;
  cardIndex: number;
  stepsIndex: number;
}

/**
 * Plugin de Remark para extrair o frontmatter YAML e armazená-lo em file.data.
 */
const extractFrontmatterPlugin = () => {
  return (tree: MdastRoot, file: VFile) => {
    visit(tree, "yaml", (node, index, parent) => {
      if (!parent || typeof index !== "number") return;

      try {
        // Parseia o conteúdo YAML e armazena
        file.data.frontmatter = load(node.value);
      } catch (e) {
        console.error("Erro ao processar o frontmatter YAML:", e);
        file.data.frontmatter = {};
      }

      // Remove o nó YAML da árvore para não ser renderizado
      parent.children.splice(index, 1);
      return ["skip", index]; // Pula o processamento do nó removido
    });
  };
};

/**
 * Unified transformation plugin - consolida todas as transformações
 */
const unifiedTransformPlugin = () => {
  return (tree: HastRoot, file: VFile) => {
    const context: TransformContext = {
      accordionIndex: 0,
      cardIndex: 0,
      stepsIndex: 0,
    };

    // 1. First pass - Insert metadata if exists
    const frontmatter = file.data.frontmatter as { [key: string]: string };
    if (frontmatter && Object.keys(frontmatter).length > 0) {
      const metadataNode: HastElement = {
        type: "element",
        tagName: "metadata",
        properties: {
          dataType: "metadata",
          ...frontmatter,
        },
        children: [],
      };
      tree.children.unshift(metadataNode, { type: "text", value: "\n\n" });
    }

    // 2. Second pass - Transform components and tables
    visit(tree, "element", (node: HastElement, index, parent) => {
      if (!parent || typeof index !== "number") return;

      switch (node.tagName.toLowerCase()) {
        case "cardlist":
          transformCardList(node, context);
          break;

        case "card":
          if (!isInsideCardList(parent)) {
            transformStandaloneCard(node, context);
          }
          break;

        case "accordion":
          transformAccordion(node, context);
          break;

        case "accordiongroup":
          transformAccordionGroup(node, context);
          break;

        case "callout":
          transformCallout(node, context);
          break;

        case "steps":
          transformSteps(node, context);
          break;

        case "tabs":
          transformTabs(node, context);
          break;

        case "image":
          transformImage(node, context);
          break;

        case "p":
          // Unwrap tables from paragraphs
          if (hasTableChild(node)) {
            unwrapTable(node, parent as HastParent, index);
            return ["skip", index];
          }
          break;

        case "br":
          // Replace <br> with <p></p>
          replaceBrWithP(parent as HastParent, index);
          return ["skip", index];
      }
    });
  };
};

// Helper functions
const isInsideCardList = (parent: HastParent): boolean => {
  return (
    parent.type === "element" &&
    (parent as HastElement).tagName.toLowerCase() === "cardlist"
  );
};

const hasTableChild = (node: HastElement): boolean => {
  return (
    node.children.length === 1 &&
    node.children[0].type === "element" &&
    node.children[0].tagName === "table"
  );
};

const unwrapTable = (
  node: HastElement,
  parent: HastParent,
  index: number
): void => {
  if ("children" in parent && node.children[0].type === "element") {
    parent.children.splice(index, 1, node.children[0]);
  }
};

const replaceBrWithP = (parent: HastParent, index: number): void => {
  const pNode: HastElement = {
    type: "element",
    tagName: "p",
    properties: {},
    children: [],
  };
  if ("children" in parent) {
    parent.children.splice(index, 1, pNode);
  }
};

const transformCardList = (
  node: HastElement,
  context: TransformContext
): void => {
  node.tagName = "cardlist";
  const newProps: Properties = {
    "data-component": "CardList",
  };

  Object.entries(node.properties || {}).forEach(([key, value]) => {
    if (key === "cols") {
      newProps[`data-${key}`] = value;
    }
  });

  // Process children Cards
  const cardsData: object[] = [];

  node.children.forEach((child) => {
    if (child.type === "element" && child.tagName.toLowerCase() === "card") {
      const card: { [key: string]: unknown } = {
        id: generateStableId("card", context.cardIndex++),
      };

      // Process Card properties
      Object.entries(child.properties || {}).forEach(([key, value]) => {
        card[toKebabCase(key)] = value;
      });

      // Process Card content
      if (child.children && child.children.length > 0) {
        const descriptionHtml = toHtml(child.children);
        if (descriptionHtml.trim()) {
          card["description"] = descriptionHtml;
        }
      }

      cardsData.push(card);
    }
  });

  newProps["data-cards"] = JSON.stringify(cardsData);
  node.properties = newProps;
  node.children = []; // Content is now in JSON
};

const transformStandaloneCard = (
  node: HastElement,
  context: TransformContext
): void => {
  node.tagName = "card";
  const newProps: Properties = {
    "data-id": generateStableId("card", context.cardIndex++),
  };

  Object.entries(node.properties || {}).forEach(([key, value]) => {
    newProps[`data-${toKebabCase(key)}`] = value;
  });

  // Process content as description
  if (node.children && node.children.length > 0) {
    const descriptionHtml = toHtml(node.children);
    if (descriptionHtml.trim()) {
      newProps["data-description"] = descriptionHtml;
    }
  }

  node.properties = newProps;
  node.children = [];
};

const transformAccordion = (
  node: HastElement,
  context: TransformContext
): void => {
  node.tagName = "div";
  const newProps: Properties = {
    dataType: "accordion",
    "data-id": generateStableId("accordion", context.accordionIndex++),
  };

  Object.entries(node.properties || {}).forEach(([key, value]) => {
    newProps[key] = value;
  });

  // Process content - preserve code blocks and HTML formatting
  const contentHtml = toHtml(node.children);
  if (contentHtml) {
    // Always preserve the original HTML structure for rich content
    newProps["description"] = contentHtml;
  }

  node.properties = newProps;
  node.children = [];
};

const transformAccordionGroup = (
  node: HastElement,
  context: TransformContext
): void => {
  node.tagName = "div";
  const newProps: Properties = {
    "data-type": "accordion-group",
    class: "accordion-group-node",
  };

  // Process accordion children
  const accordionsData: object[] = [];
  const accordionElements: HastElement[] = [];

  node.children.forEach((child) => {
    if (
      child.type === "element" &&
      child.tagName.toLowerCase() === "accordion"
    ) {
      const accordion: { [key: string]: unknown } = {
        id: generateStableId("accordion", context.accordionIndex++),
        isExpanded: false,
      };

      // Process Accordion properties
      Object.entries(child.properties || {}).forEach(([key, value]) => {
        accordion[key] = value;
      });

      // Process Accordion content
      if (child.children && child.children.length > 0) {
        const descriptionHtml = toHtml(child.children);
        if (descriptionHtml.trim()) {
          accordion["description"] = descriptionHtml;
        }
      }

      accordionsData.push(accordion);

      // Create corresponding div element
      accordionElements.push({
        type: "element",
        tagName: "div",
        properties: {
          "data-type": "accordion",
          title: String(accordion.title || ""),
          description: String(accordion.description || ""),
        },
        children: [],
      });
    }
  });

  newProps["data-accordions"] = JSON.stringify(accordionsData);
  node.properties = newProps;
  node.children = accordionElements;
};

const transformCallout = (
  node: HastElement,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _context: TransformContext
): void => {
  node.tagName = "div";
  const newProps: Properties = {
    "data-type": "callout",
    class: "callout-node",
  };

  Object.entries(node.properties || {}).forEach(([key, value]) => {
    if (key === "type") {
      // Store type directly as an attribute for proper HTML rendering
      newProps["type"] = value;
    } else if (key === "title") {
      // Store title as both attribute and data-attribute
      newProps["title"] = value;
      newProps["data-title"] = value;
    }
  });

  // Process content as description - preserve HTML formatting
  if (node.children && node.children.length > 0) {
    const descriptionHtml = toHtml(node.children);
    if (descriptionHtml.trim()) {
      // Preserve original HTML structure
      newProps["description"] = descriptionHtml;
      newProps["data-description"] = descriptionHtml;
    }
  }

  node.properties = newProps;
  node.children = [];
};

const transformSteps = (node: HastElement, context: TransformContext): void => {
  node.tagName = "div";
  const newProps: Properties = {
    "data-type": "steps",
    class: "steps-node",
    "data-id": generateStableId("steps", context.stepsIndex++),
  };

  let stepIdCounter = 0;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const parseSteps = (children: (HastElement | HastText)[]): any[] => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const stepsData: any[] = [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let currentStep: any = null;
    let currentContent: (HastElement | HastText)[] = [];

    children.forEach((child) => {
      if (child.type === "element") {
        const headerTags = ["h2", "h3", "h4", "h5", "h6"];
        if (headerTags.includes(child.tagName)) {
          if (currentStep) {
            currentStep.content = convertBrToPInString(
              currentContent.map((c) => toHtml(c)).join("")
            );
            stepsData.push(currentStep);
          }

          currentStep = {
            id: `step-${Date.now()}-${stepIdCounter++}`,
            title:
              child.children
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                .map((c: any) => (c.type === "text" ? c.value : ""))
                .join("")
                .trim() || `Step ${stepsData.length + 1}`,
            titleSize: child.tagName,
            subSteps: [],
          };
          currentContent = [];
        } else if (child.tagName.toLowerCase() === "steps") {
          if (currentStep) {
            currentStep.subSteps = parseSteps(
              child.children as (HastElement | HastText)[]
            );
          }
        } else {
          if (currentStep) {
            currentContent.push(child);
          }
        }
      } else if (child.type === "text" && child.value.trim() !== "") {
        if (currentStep) {
          currentContent.push(child);
        }
      }
    });

    if (currentStep) {
      currentStep.content = convertBrToPInString(
        currentContent.map((c) => toHtml(c)).join("")
      );
      stepsData.push(currentStep);
    }

    return stepsData;
  };

  const stepsData = parseSteps(node.children as (HastElement | HastText)[]);
  newProps["data-steps"] = JSON.stringify(stepsData);
  node.properties = newProps;
  node.children = [];
};

const transformTabs = (
  node: HastElement,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _context: TransformContext
): void => {
  node.tagName = "div";
  const newProps: Properties = {
    "data-type": "tabs",
    class: "tabs-node",
  };

  // Process children to extract tabs with individual properties
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const tabsData: any[] = [];

  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      if (child.type === "element" && child.tagName.toLowerCase() === "tab") {
        const tab: { [key: string]: unknown } = {
          id: generateStableId("tab", tabsData.length),
        };

        // Process Tab properties
        Object.entries(child.properties || {}).forEach(([key, value]) => {
          if (key === "value") {
            tab.value = value;
          } else if (key === "label") {
            tab.label = value;
          } else if (key === "default") {
            tab.default = value === "true" || value === true;
          }
        });

        // Ensure label exists - use value as fallback
        if (!tab.label) {
          tab.label = tab.value || `Tab ${tabsData.length + 1}`;
        }

        // Process Tab content
        if (child.children && child.children.length > 0) {
          const contentHtml = toHtml(child.children);
          if (contentHtml.trim()) {
            tab.content = convertBrToPInString(contentHtml);
          }
        }

        tabsData.push(tab);
      }
    });
  }

  newProps["data-tabs"] = JSON.stringify(tabsData);
  node.properties = newProps;
  node.children = []; // Content is now in JSON
};

const transformImage = (
  node: HastElement,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _context: TransformContext
): void => {
  node.tagName = "div";
  const newProps: Properties = {
    "data-type": "image",
    class: "image-node",
  };

  // Store the size for the wrapper
  if (node.properties?.size) {
    newProps["data-size"] = node.properties.size;
  }

  // Create img element as child
  const imgProps: Properties = {};

  Object.entries(node.properties || {}).forEach(([key, value]) => {
    if (key === "src") {
      imgProps["src"] = value;
    } else if (key === "srcDark") {
      imgProps["data-src-dark"] = value;
    } else if (key === "alt") {
      imgProps["alt"] = value;
    } else if (key === "size") {
      imgProps["style"] = `width: ${value}; max-width: 100%;`;
    }
  });

  node.properties = newProps;
  node.children = [
    {
      type: "element",
      tagName: "img",
      properties: imgProps,
      children: [],
    },
  ];
};

/**
 * Função principal para converter um string MDX em um string HTML.
 * Utiliza a pipeline do `unified` para uma conversão segura e estruturada.
 */
export const mdxToHtml = async (mdxContent: string): Promise<string> => {
  if (!mdxContent) return "";

  console.log("🔄 Processando conteúdo MDX para HTML:", mdxContent);

  // Debug: Check if MDX contains Callout
  if (mdxContent.includes("<Callout")) {
    console.log("📍 Found Callout in MDX content");
  }

  const file = await unified()
    // 1. Parse de MDX -> MDAST (Árvore de Markdown)
    .use(remarkParse)
    .use(remarkMdx) // Habilita o parsing de sintaxe JSX
    .use(remarkFrontmatter, ["yaml"]) // Reconhece blocos ---yaml---

    // 2. Plugin customizado para extrair dados do frontmatter
    .use(extractFrontmatterPlugin)

    // 3. Conversão de MDAST -> HAST (Árvore de HTML)
    .use(remarkRehype, {
      allowDangerousHtml: true, // Permite que HTML bruto no markdown passe para o HAST
      handlers: {
        // Handler for code blocks to preserve line breaks and language
        code(h, node) {
          // Create the code element with proper attributes
          const codeElement: HastElement = {
            type: "element",
            tagName: "code",
            properties: node.lang
              ? { className: [`language-${node.lang}`] }
              : {},
            children: [{ type: "text", value: node.value }],
          };

          const pre: HastElement = {
            type: "element",
            tagName: "pre",
            properties: {
              "data-type": "codeBlock",
              "data-language": node.lang || "text",
            },
            children: [codeElement],
          };

          return pre;
        },
        // Handler para componentes JSX de bloco (ex: <Card />)
        mdxJsxFlowElement(h, node) {
          const attributes = (node.attributes || []).reduce(
            (acc: Properties, attr: MdxJsxAttribute) => {
              // Trata valores de expressão, como `cols={2}`
              if (
                typeof attr.value === "object" &&
                attr.value !== null &&
                "value" in attr.value
              ) {
                acc[attr.name] = attr.value.value as string | number | boolean;
              } else {
                acc[attr.name] = attr.value;
              }
              return acc;
            },
            {} as Properties
          );

          // Special handling for Tabs and Tab components
          if (node.name === "Tabs") {
            return {
              type: "element",
              tagName: "tabs",
              properties: attributes,
              children: h.all(node) as HastElement["children"],
            };
          } else if (node.name === "Tab") {
            return {
              type: "element",
              tagName: "tab",
              properties: attributes,
              children: h.all(node) as HastElement["children"],
            };
          }

          return {
            type: "element",
            tagName: node.name || "div",
            properties: attributes,
            children: h.all(node) as HastElement["children"],
          };
        },
        // Handler para componentes JSX inline (ex: <strong className="special">...</strong>)
        mdxJsxTextElement(h, node) {
          const attributes = (node.attributes || []).reduce(
            (acc: Properties, attr: MdxJsxAttribute) => {
              // Trata valores de expressão, como `color={"red"}`
              if (
                typeof attr.value === "object" &&
                attr.value !== null &&
                "value" in attr.value
              ) {
                acc[attr.name] = attr.value.value as string | number | boolean;
              } else {
                acc[attr.name] = attr.value;
              }
              return acc;
            },
            {} as Properties
          );

          return {
            type: "element",
            tagName: node.name || "span", // 'span' é um padrão seguro para elementos inline
            properties: attributes,
            children: h.all(node) as HastElement["children"],
          };
        },
      },
    })

    // 4. Plugin customizado unificado para todas as transformações
    .use(unifiedTransformPlugin)
    .use(rehypeStringify, { allowDangerousHtml: true })
    .process(mdxContent);

  const htmlOutput = String(file);

  // Debug: Check Callout conversion result
  if (
    mdxContent.includes("<Callout") &&
    htmlOutput.includes('data-type="callout"')
  ) {
    console.log("✅ Callout converted successfully");
    // Extract and log the callout attributes for debugging
    const calloutMatch = htmlOutput.match(/<div[^>]*data-type="callout"[^>]*>/);
    if (calloutMatch) {
      console.log("📌 Callout HTML attributes:", calloutMatch[0]);
    }
  }

  console.log("✅ Converted HTML output:", htmlOutput);
  return htmlOutput;
};
