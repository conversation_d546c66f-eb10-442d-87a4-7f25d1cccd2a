interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export const validateHtmlForMdx = (html: string): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check for empty input
  if (!html || html.trim() === "") {
    errors.push("HTML input is empty");
    return { isValid: false, errors, warnings };
  }

  // Check for malformed HTML
  const openTags = html.match(/<[^/][^>]*>/g) || [];
  const closeTags = html.match(/<\/[^>]+>/g) || [];

  if (openTags.length !== closeTags.length) {
    warnings.push("Possible unclosed HTML tags detected");
  }

  // Check for unsupported components
  const unsupportedComponents = ["script", "style", "iframe"];
  unsupportedComponents.forEach((tag) => {
    if (html.includes(`<${tag}`)) {
      errors.push(`Unsupported tag: <${tag}>`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

export const validateMdxForHtml = (mdx: string): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check for empty input
  if (!mdx || mdx.trim() === "") {
    errors.push("MDX input is empty");
    return { isValid: false, errors, warnings };
  }

  // Check for valid component syntax
  const componentRegex = /<([A-Z][a-zA-Z]*)[^>]*>/g;
  const matches = mdx.match(componentRegex) || [];

  const supportedComponents = [
    "Card",
    "CardList",
    "Accordion",
    "AccordionGroup",
  ];
  matches.forEach((match) => {
    const componentName = match.match(/<([A-Z][a-zA-Z]*)/)?.[1];
    if (componentName && !supportedComponents.includes(componentName)) {
      warnings.push(`Unknown component: ${componentName}`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};
