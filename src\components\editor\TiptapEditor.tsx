"use client";

import { use<PERSON><PERSON><PERSON>, EditorContent, type Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Table from "@tiptap/extension-table";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import Placeholder from "@tiptap/extension-placeholder";
import Link from "@tiptap/extension-link";
import HardBreak from "@tiptap/extension-hard-break";
import React, { useEffect } from "react";
import {
  Slash,
  SlashCmd,
  SlashCmdProvider,
  enableKeyboardNavigation,
} from "@harshtalks/slash-tiptap";

import MetadataNode from "./extensions/MetadataNode";
import MetadataExtension from "./extensions/MetadataExtension";
import CodeBlockNode from "./extensions/CodeBlockNode";
import CardNode from "./extensions/CardNode";
import Card<PERSON>istNode from "./extensions/CardListNode";
import AccordionNode from "./extensions/AccordionNode";
import AccordionGroupNode from "./extensions/AccordionGroupNode";
import CalloutNode from "./extensions/CalloutNode";
import ImageNode from "./extensions/ImageNode";
import VideoNode from "./extensions/VideoNode";
import StepsNode from "./extensions/StepsNode";
import TabsNode from "./extensions/TabsNode";
import "./TiptapEditor.css";

// Import utilities
// import { htmlToMdx } from "./utils/htmlToMdx";
import { mdxToHtml } from "./utils/mdxToHtml";
import { getContextualSuggestions } from "./utils/contextualSuggestions";
import { getCommandIcon } from "./utils/commandIcons";
import BubbleMenu from "./components/BubbleMenu";
import LinkDialog from "./components/LinkDialog";
import ShortcutsTip from "./components/ShortcutsTip";
import type { TiptapEditorProps } from "./types";

const TiptapEditor = ({
  content = "",
  editable = true,
  onUpdate,
}: TiptapEditorProps) => {
  const [isLinkDialogOpen, setIsLinkDialogOpen] = React.useState(false);
  const [linkDialogData, setLinkDialogData] = React.useState<{
    editor: Editor | null;
    selectedText: string;
  }>({ editor: null, selectedText: "" });
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // Disable default code block
      }),
      Placeholder.configure({
        placeholder: "Press / for commands...",
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: "editor-link",
        },
      }),
      Slash.configure({
        suggestion: {
          items: ({ editor }: { editor: Editor | null }) =>
            getContextualSuggestions(editor),
        },
      }),
      Table.configure({
        resizable: false,
      }),
      TableRow,
      TableHeader,
      TableCell,
      MetadataNode,
      MetadataExtension,
      CodeBlockNode, // Use our custom code block
      CardNode,
      CardListNode,
      AccordionNode,
      AccordionGroupNode,
      CalloutNode,
      ImageNode,
      VideoNode,
      StepsNode,
      TabsNode,
      HardBreak,
    ],
    content: content,
    editable: editable,
    onUpdate: async ({ editor }) => {
      if (onUpdate) {
        // Dynamic import to resolve module issue
        const { htmlToMdx } = await import("./utils/htmlToMdx");

        // Create a modified editor object with MDX output
        const modifiedEditor = {
          ...editor,
          getHTML: async () => await htmlToMdx(editor.getHTML()),
        };
        onUpdate(modifiedEditor as unknown as Editor);
      }
      console.log(editor.getJSON());
    },
    editorProps: {
      handleDOMEvents: {
        keydown: (_, ev) => enableKeyboardNavigation(ev),
      },
    },
    immediatelyRender: false,
  });

  useEffect(() => {
    const updateContent = async () => {
      if (!editor || !content) {
        return;
      }

      // Dynamic import to resolve module issue
      const { htmlToMdx } = await import("./utils/htmlToMdx");

      // Convert the editor's current HTML content to MDX for a correct comparison
      const currentMdx = await htmlToMdx(editor.getHTML());

      // Only update the editor if the incoming content is genuinely different
      if (currentMdx === content) {
        return;
      }

      // Use the utility function to convert MDX to HTML
      const processedContent = await mdxToHtml(content);

      // Use queueMicrotask to defer execution outside React lifecycle
      queueMicrotask(() => {
        // Set content without triggering another update to prevent loops
        editor.commands.setContent(processedContent, false);
      });
    };
    updateContent();
  }, [content, editor]);

  useEffect(() => {
    return () => {
      editor?.destroy();
    };
  }, [editor]);

  // Handle link dialog events
  useEffect(() => {
    const handleLinkDialog = (event: CustomEvent) => {
      const { editor: eventEditor, selectedText = "" } = event.detail;
      setLinkDialogData({ editor: eventEditor, selectedText });
      setIsLinkDialogOpen(true);
    };

    window.addEventListener(
      "openLinkDialog",
      handleLinkDialog as EventListener
    );

    return () => {
      window.removeEventListener(
        "openLinkDialog",
        handleLinkDialog as EventListener
      );
    };
  }, []);

  const handleLinkConfirm = (url: string, text: string) => {
    const { editor: dialogEditor, selectedText } = linkDialogData;
    if (dialogEditor) {
      if (selectedText) {
        // Replace selected text with link
        dialogEditor
          .chain()
          .focus()
          .extendMarkRange("link")
          .setLink({ href: url })
          .insertContent(text)
          .run();
      } else {
        // Insert new link
        dialogEditor
          .chain()
          .focus()
          .insertContent(`<a href="${url}">${text}</a>`)
          .run();
      }
    }
    setIsLinkDialogOpen(false);
  };

  // Smart positioning for slash menu
  useEffect(() => {
    if (!editor) return;

    const observeSlashMenu = () => {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              const slashMenu =
                element.querySelector("[cmdk-root]") ||
                (element.hasAttribute && element.hasAttribute("cmdk-root")
                  ? element
                  : null);

              if (slashMenu) {
                adjustSlashMenuPosition(slashMenu as HTMLElement);
              }
            }
          });
        });
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      return () => observer.disconnect();
    };

    const adjustSlashMenuPosition = (menu: HTMLElement) => {
      // Small delay to ensure the menu is fully rendered
      setTimeout(() => {
        const rect = menu.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        const spaceBelow = viewportHeight - rect.bottom;
        const spaceAbove = rect.top;
        const spaceRight = viewportWidth - rect.right;
        const spaceLeft = rect.left;

        // Reset any previous positioning attributes
        menu.removeAttribute("data-position");

        // Check if menu goes below viewport
        if (spaceBelow < 20 && spaceAbove > spaceBelow) {
          menu.setAttribute("data-position", "top");
        }

        // Check if menu goes beyond right edge
        if (spaceRight < 20 && spaceLeft > 340) {
          // 340 = menu width + some padding
          const currentPosition = menu.getAttribute("data-position") || "";
          menu.setAttribute("data-position", currentPosition + " left");
        }

        // Ensure z-index is high enough
        menu.style.zIndex = "9999";

        // Add smooth transition
        menu.style.transition =
          "transform 0.2s ease-out, opacity 0.2s ease-out";
      }, 10);
    };

    const cleanup = observeSlashMenu();

    return cleanup;
  }, [editor]);

  // Função para focar o editor quando clicar em áreas vazias
  const handleContainerClick = (e: React.MouseEvent) => {
    if (editor && e.target === e.currentTarget) {
      editor.commands.focus("end");
    }
  };

  return (
    <SlashCmdProvider>
      <div className="prose dark:prose-invert max-w-none h-full overflow-y-auto relative">
        <div className="max-w-[1024px] mx-auto">
          <div
            className="min-h-[calc(100vh-110px)] pb-96 cursor-text"
            onClick={handleContainerClick}
          >
            <EditorContent
              editor={editor}
              className="min-h-[calc(100vh-110px)] focus-within:outline-none"
            />
            {/* Espaçamento adicional para melhor experiência de scroll */}
            <div className="h-64 cursor-text" onClick={handleContainerClick} />
          </div>
          {editor && <BubbleMenu editor={editor} />}
          {editor && (
            <SlashCmd.Root editor={editor}>
              <SlashCmd.Cmd>
                <SlashCmd.Empty>No commands available</SlashCmd.Empty>
                <SlashCmd.List>
                  {getContextualSuggestions(editor).map((item) => (
                    <SlashCmd.Item
                      value={item.title}
                      onCommand={(val) => {
                        item.command(val);
                      }}
                      key={item.title}
                    >
                      <div className="flex items-center w-full">
                        {getCommandIcon(item.title)}
                        <span className="text-sm font-medium">
                          {item.title}
                        </span>
                      </div>
                    </SlashCmd.Item>
                  ))}
                </SlashCmd.List>
              </SlashCmd.Cmd>
            </SlashCmd.Root>
          )}

          <LinkDialog
            isOpen={isLinkDialogOpen}
            onClose={() => setIsLinkDialogOpen(false)}
            onConfirm={handleLinkConfirm}
            initialText={linkDialogData.selectedText}
          />
        </div>

        {/* Shortcuts Tip */}
        <ShortcutsTip />
      </div>
    </SlashCmdProvider>
  );
};

export default TiptapEditor;
