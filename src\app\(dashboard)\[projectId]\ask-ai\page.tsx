"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useProject } from "@/contexts/ProjectContext/ProjectContextProvider";
// import { Project } from "@/contexts/ProjectContext/types"; // Project type is available via selectedProject from useProject hook
import AskAIStatsTab, {
	BotStatistics,
} from "@/components/ask-ai/AskAIStatsTab";
import AskAILogsTab from "@/components/ask-ai/AskAILogsTab";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	BotQuestion,
	DocsBotPagination,
	DocsBotQuestionsResponse,
} from "./types"; // Importado tipos para Logs
import { useSiteTag } from "@/hooks/useSiteTag"; // Import the siteTag hook
import { shouldUseMockData } from "@/utils/mocks/mockProjects";
import {
	generateMockAskAiStats,
	generateMockAskAiLogs,
} from "@/utils/mocks/askAiMockData";
// Card não é mais usado para o container principal, mas AskAIStatsTab pode usar internamente ou outros componentes.
// Considerar re-importar Card de "@/components/ui/card" se necessário em AskAIStatsTab ou outros.

type TabType = "stats" | "logs" | "design";

// Expected structure from our /api/ask-ai/stats endpoint
interface StatsApiResponse {
	success: boolean;
	data: BotStatistics | null; // Assuming the 'data' field directly matches BotStatistics or is null
	message?: string; // Optional error message from our API
}

// Interface para a resposta da API de logs/perguntas
interface LogsApiResponse {
	success: boolean;
	data: DocsBotQuestionsResponse | null; // data agora é DocsBotQuestionsResponse
	message?: string;
}

// For handling potential error structure from fetch .json() errors
interface ApiErrorPayload {
	message?: string;
	// Allow other properties if the error structure is more complex
	[key: string]: unknown;
}

// Valores default para filtros de logs
const initialLogFilters = {
	rating: "all",
	escalation: "all",
	couldAnswer: "all",
};

const AskAIPage = () => {
	const { selectedProject, isLoadingProjects } = useProject();
	const { siteTag, siteName, isAvailable, hasSiteTag } = useSiteTag(); // Example: Access cached siteTag for Web Analytics
	const [activeTab, setActiveTab] = useState<TabType>("stats");
	const [timeDelta, setTimeDelta] = useState("30");
	const [statistics, setStatistics] = useState<BotStatistics | null>(null);
	const [isLoadingStats, setIsLoadingStats] = useState(true);
	const [errorStats, setErrorStats] = useState<string | null>(null);

	// Example: You could use siteTag here for any Web Analytics API calls
	useEffect(() => {
		if (hasSiteTag && siteTag) {
			// You could use siteTag here for any Web Analytics API calls
			// Example: fetch analytics data for Ask AI usage tracking
		}
	}, [siteTag, siteName, isAvailable, hasSiteTag]);

	// Estados para a aba de Logs
	const [questions, setQuestions] = useState<BotQuestion[]>([]);
	const [logsPagination, setLogsPagination] =
		useState<DocsBotPagination | null>(null);
	const [currentLogsPage, setCurrentLogsPage] = useState(0); // API usa page base 0
	const [isLoadingLogs, setIsLoadingLogs] = useState(false); // Inicia como false, carrega ao abrir a tab
	const [errorLogs, setErrorLogs] = useState<string | null>(null);
	const [logFilters, setLogFilters] = useState(initialLogFilters);
	const [logDateRange, setLogDateRange] = useState<{
		startDate: Date | undefined;
		endDate: Date | undefined;
	}>({ startDate: undefined, endDate: undefined });

	const fetchStatistics = useCallback(
		async (docsBotKey: string, currentDelta: string) => {
			setIsLoadingStats(true);
			setErrorStats(null);

			try {
				// Check if project should use mock data
				if (
					selectedProject &&
					shouldUseMockData(selectedProject.project_name)
				) {
					// Use mock data instead of API call
					const mockStats = generateMockAskAiStats(
						selectedProject.project_name,
						currentDelta
					);
					setStatistics(mockStats);
					setIsLoadingStats(false);
					return;
				}

				const response = await fetch(
					`/api/ask-ai/stats?docsbotKey=${encodeURIComponent(
						docsBotKey
					)}&timeDelta=${encodeURIComponent(currentDelta)}`
				);

				if (!response.ok) {
					let errorPayload: ApiErrorPayload;
					try {
						errorPayload = (await response.json()) as ApiErrorPayload;
						if (typeof errorPayload.message !== "string") {
							errorPayload.message =
								"Error response did not contain a valid message.";
						}
					} catch {
						errorPayload = {
							message: `Failed to parse error JSON. Status: ${response.status}`,
						};
					}
					throw new Error(
						errorPayload.message || `HTTP error! status: ${response.status}`
					);
				}

				const responseData = (await response.json()) as StatsApiResponse;

				if (!responseData.success || !responseData.data) {
					// Use message from API response if available, otherwise a generic one
					throw new Error(
						responseData.message ||
							"API returned unsuccessful status or no data."
					);
				}

				setStatistics(responseData.data);
			} catch (err: unknown) {
				let errorMessage =
					"An unexpected error occurred while fetching statistics.";
				if (err instanceof Error) {
					errorMessage = err.message;
				}
				console.error("Error in fetchStatistics:", err);
				setErrorStats(errorMessage);
				setStatistics(null);
			} finally {
				setIsLoadingStats(false);
			}
		},
		[selectedProject]
	);

	const fetchLogs = useCallback(
		async (
			docsBotKey: string,
			page: number,
			filters: typeof initialLogFilters,
			dateRange: { startDate: Date | undefined; endDate: Date | undefined }
		) => {
			setIsLoadingLogs(true);
			setErrorLogs(null);

			try {
				// Check if project should use mock data
				if (
					selectedProject &&
					shouldUseMockData(selectedProject.project_name)
				) {
					// Use mock data instead of API call
					const mockLogs = generateMockAskAiLogs(
						selectedProject.project_name,
						page,
						filters,
						dateRange
					);
					setQuestions(mockLogs.questions);
					setLogsPagination(mockLogs.pagination);
					setIsLoadingLogs(false);
					return;
				}

				let url = `/api/ask-ai/questions?docsbotKey=${encodeURIComponent(
					docsBotKey
				)}&page=${page}`;

				if (filters.rating !== "all") {
					const ratingValue =
						filters.rating === "positive"
							? "1"
							: filters.rating === "negative"
							? "-1"
							: filters.rating === "neutral"
							? "0"
							: "";
					if (ratingValue) url += `&rating=${ratingValue}`;
				}
				if (filters.escalation !== "all")
					url += `&escalated=${filters.escalation === "escalated"}`;
				if (filters.couldAnswer !== "all")
					url += `&couldAnswer=${filters.couldAnswer === "yes"}`;

				if (dateRange.startDate && dateRange.endDate) {
					url += `&startDate=${
						dateRange.startDate.toISOString().split("T")[0]
					}`;
					const endOfDay = new Date(dateRange.endDate);
					endOfDay.setHours(23, 59, 59, 999);
					url += `&endDate=${endOfDay.toISOString()}`;
				}

				const response = await fetch(url);
				if (!response.ok) {
					let errorPayload: ApiErrorPayload;
					try {
						errorPayload = (await response.json()) as ApiErrorPayload;
						if (typeof errorPayload.message !== "string") {
							errorPayload.message =
								"Error response did not contain a valid message.";
						}
					} catch {
						errorPayload = {
							message: `Failed to parse error JSON. Status: ${response.status}`,
						};
					}
					throw new Error(
						errorPayload.message || `HTTP error! status: ${response.status}`
					);
				}
				const responseData = (await response.json()) as LogsApiResponse;
				if (!responseData.success || !responseData.data) {
					throw new Error(
						responseData.message ||
							"API returned unsuccessful status or no logs data."
					);
				}
				setQuestions(responseData.data.questions);
				setLogsPagination(responseData.data.pagination);
			} catch (err: unknown) {
				const errorMessage =
					err instanceof Error
						? err.message
						: "An unexpected error occurred while fetching logs.";
				console.error("Error in fetchLogs:", err);
				setErrorLogs(errorMessage);
				setQuestions([]);
				setLogsPagination(null);
			} finally {
				setIsLoadingLogs(false);
			}
		},
		[selectedProject]
	);

	useEffect(() => {
		const docsBotString = selectedProject?.plan_json?.docsbot;
		const isProjectMocked =
			selectedProject && shouldUseMockData(selectedProject.project_name);

		if (
			selectedProject &&
			(docsBotString || isProjectMocked) &&
			!isLoadingProjects
		) {
			if (activeTab === "stats") {
				fetchStatistics(docsBotString || "mock_key", timeDelta);
			} else if (activeTab === "logs") {
				fetchLogs(
					docsBotString || "mock_key",
					currentLogsPage,
					logFilters,
					logDateRange
				);
			}
		} else if (!isLoadingProjects) {
			setIsLoadingStats(false);
			setStatistics(null);
			setIsLoadingLogs(false);
			setQuestions([]);
			setLogsPagination(null);
			if (!selectedProject) {
				setErrorStats("No project selected or project data is unavailable.");
				setErrorLogs("No project selected or project data is unavailable.");
			} else if (!docsBotString && !isProjectMocked) {
				setErrorStats(
					"Ask AI feature is not configured for this project. Please set up the DocsBot integration in the project settings."
				);
				setErrorLogs(
					"Ask AI feature is not configured for this project. Please set up the DocsBot integration in the project settings."
				);
			}
		}
	}, [
		selectedProject,
		timeDelta,
		isLoadingProjects,
		activeTab,
		fetchStatistics,
		fetchLogs,
		currentLogsPage,
		logFilters,
		logDateRange,
	]);

	const handleTabChange = (tab: TabType) => setActiveTab(tab);
	const handlePeriodChange = (value: string) => {
		setTimeDelta(value);
	};

	// Callbacks para AskAILogsTab
	const handleLogsPageChange = (page: number) => {
		setCurrentLogsPage(page);
	};
	const handleRatingFilterChange = (rating: string) => {
		setLogFilters((prev) => ({ ...prev, rating }));
		setCurrentLogsPage(0); // Reset page on filter change
	};
	const handleEscalationFilterChange = (escalation: string) => {
		setLogFilters((prev) => ({ ...prev, escalation }));
		setCurrentLogsPage(0);
	};
	const handleCouldAnswerFilterChange = (couldAnswer: string) => {
		setLogFilters((prev) => ({ ...prev, couldAnswer }));
		setCurrentLogsPage(0);
	};
	const handleDateRangeChange = (startDate?: Date, endDate?: Date) => {
		setLogDateRange({ startDate, endDate });
		setCurrentLogsPage(0);
	};

	// Case: Project selected, but Ask AI not configured for it (and not a mock project)
	if (
		selectedProject &&
		!selectedProject.plan_json?.docsbot &&
		!shouldUseMockData(selectedProject.project_name) &&
		!isLoadingProjects
	) {
		return (
			<div className='w-full p-4 md:p-6 lg:p-8 space-y-6 text-center'>
				<h1 className='text-2xl md:text-3xl font-semibold text-foreground mb-4'>
					Ask AI Configuration Needed
				</h1>
				<div className='bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md'>
					<p className='text-muted-foreground'>
						Ask AI feature is not configured for project:{" "}
						<strong>{selectedProject.project_name}</strong>.
					</p>
					<p className='text-sm text-muted-foreground mt-2'>
						Please set up the DocsBot integration in the project settings.
					</p>
				</div>
			</div>
		);
	}

	// Case: No project could be selected/loaded (and not in initial loading state anymore)
	if (!selectedProject && !isLoadingProjects) {
		return (
			<div className='w-full p-4 md:p-6 lg:p-8 text-center'>
				No project selected or project data is unavailable. Please select a
				project or check your access.
			</div>
		);
	}

	const TabButton = ({ tab, label }: { tab: TabType; label: string }) => (
		<button
			onClick={() => handleTabChange(tab)}
			className={`px-4 py-2 text-sm font-medium rounded-t-md transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border-b-2 ${
				activeTab === tab
					? "border-blue-600 text-blue-600 cursor-default"
					: "border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground/50 cursor-pointer"
			}`}
		>
			{label}
		</button>
	);

	const disableStatsPeriodSelector = isLoadingStats || !!errorStats;

	return (
		<div className='w-full space-y-6'>
			<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6'>
				<h1 className='md:text-3xl text-2xl font-semibold text-wd-font-color'>
					Ask AI Stats
				</h1>
				{activeTab === "stats" && (
					<div className='flex items-center space-x-2'>
						<span className='text-sm text-muted-foreground'>Period:</span>
						<Select
							value={timeDelta}
							onValueChange={handlePeriodChange}
							disabled={disableStatsPeriodSelector}
						>
							<SelectTrigger className='w-[180px] bg-white'>
								<SelectValue placeholder='Select period' />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value='7'>Last 7 days</SelectItem>
								<SelectItem value='30'>Last 30 days</SelectItem>
								<SelectItem value='90'>Last 90 days</SelectItem>
							</SelectContent>
						</Select>
					</div>
				)}
			</div>

			<div className='border-b border-border flex space-x-1 mb-6'>
				<TabButton tab='stats' label='Stats' />
				<TabButton tab='logs' label='Logs' />
				<TabButton tab='design' label='Design' />
			</div>

			<div>
				{activeTab === "stats" && (
					<>
						{errorStats && !isLoadingStats && (
							<div className='w-full p-4 md:p-6 lg:p-8 space-y-6 text-center'>
								<h2 className='text-xl font-semibold text-destructive mb-3'>
									Could not load statistics
								</h2>
								<div className='bg-destructive/10 p-4 rounded-lg border border-destructive/30'>
									<p className='text-destructive-foreground text-sm'>
										{errorStats}
									</p>
								</div>
							</div>
						)}
						{!errorStats && (
							<AskAIStatsTab
								statistics={statistics}
								isLoading={isLoadingStats}
							/>
						)}
					</>
				)}
				{activeTab === "logs" && (
					<>
						{errorLogs && !isLoadingLogs && (
							<div className='w-full p-4 md:p-6 lg:p-8 space-y-6 text-center'>
								<h2 className='text-xl font-semibold text-destructive mb-3'>
									Could not load logs
								</h2>
								<div className='bg-destructive/10 p-4 rounded-lg border border-destructive/30'>
									<p className='text-destructive-foreground text-sm'>
										{errorLogs}
									</p>
								</div>
							</div>
						)}
						{/* Renderiza AskAILogsTab mesmo que errorLogs exista, para que os filtros ainda possam ser usados para tentar novamente */}
						{/* Ou se não estiver carregando e não houver erro (ou se houver erro mas quisermos mostrar filtros) */}
						{(!isLoadingLogs || questions.length > 0 || errorLogs) && (
							<AskAILogsTab
								questions={questions}
								isLoading={isLoadingLogs} // Passa o estado de carregamento para o componente filho
								pagination={logsPagination}
								currentPage={currentLogsPage}
								onPageChange={handleLogsPageChange}
								filters={logFilters}
								dateRange={logDateRange}
								onRatingFilterChange={handleRatingFilterChange}
								onEscalationFilterChange={handleEscalationFilterChange}
								onCouldAnswerFilterChange={handleCouldAnswerFilterChange}
								onDateRangeChange={handleDateRangeChange}
							/>
						)}
					</>
				)}
				{activeTab === "design" && (
					<div className='text-center py-10'>
						<p className='text-muted-foreground'>
							Design tab is under construction.
						</p>
					</div>
				)}
			</div>
		</div>
	);
};

export default AskAIPage;
